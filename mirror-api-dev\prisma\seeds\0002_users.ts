import { hash, genSalt } from "bcryptjs";

import { prisma } from "../seed";

export async function users() {
    const salt = await genSalt(8);
    const adminPassword = await hash("admin123" + salt, 8);
    const userPassword = await hash("aaAA**11" + salt, 8);

    console.log("usuarios");

    // Limpar dados existentes
    await prisma.roleUser.deleteMany();
    await prisma.user.deleteMany();

    // Criar usuário admin com senha "admin"
    const adminUser = await prisma.user.create({
        data: {
            id: "admin-user-id-12345",
            phone: "11999999999",
            first_name: "Admin",
            last_name: "System",
            document: "00000000000",
            email: "<EMAIL>",
            password: adminPassword,
            role: "admin",
            notify: true,
            status: true,
            salt,
        },
    });

    // Associar usuário admin ao role admin
    await prisma.roleUser.create({
        data: {
            user_id: adminUser.id,
            role_id: "aab56696-90a4-4b07-869d-7f051871c81c", // ID do role Admin
        },
    });

    await prisma.user.create({
        data: {
            phone: "5521964276349-1493168921",
            first_name: "Seguradora",
            last_name: "ME",
            document: "41105887073",
            email: "<EMAIL>",
            password: userPassword,
            role: "user",
            notify: false,
            status: false,
            salt,
        },
    });

    await prisma.user.create({
        data: {
            id: "ddbb04d4-deaf-4e33-af64-c49f25cf43f2",
            phone: "5521964276349",
            first_name: "Admin",
            last_name: "Admin",
            document: "08931946708",
            email: "<EMAIL>",
            password: userPassword,
            role: "user",
            notify: true,
            salt,
        },
    });

    await prisma.user.create({
        data: {
            phone: "5521971674310",
            first_name: "Duarte",
            last_name: "ME",
            document: "73818303703",
            email: "<EMAIL>",
            password: userPassword,
            role: "user",
            notify: false,
            status: false,
            salt,
        },
    });
}
