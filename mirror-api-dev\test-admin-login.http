### Test Admin Login
POST http://localhost:3000/auth/sign-in
Content-Type: application/json

{
  "user": "<EMAIL>",
  "password": "admin"
}

### Test Admin Access to Users
GET http://localhost:3000/users
Authorization: Bearer {{access_token}}

### Test Admin Access to Permissions
GET http://localhost:3000/auth/permissions
Authorization: Bearer {{access_token}}

### Test Admin Access to Roles
GET http://localhost:3000/auth/roles
Authorization: Bearer {{access_token}}
