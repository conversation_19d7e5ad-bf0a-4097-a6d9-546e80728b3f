import { prisma } from "../seed";

export async function permissions() {
    console.log("permissions");

    // Limpar dados existentes
    await prisma.permissionRole.deleteMany();
    await prisma.permission.deleteMany();

    // Criar todas as permissões necessárias
    const permissionsData = [
        // Permissões de usuários
        { name: "Criar usuário", slug: "c-user", description: "Permissão para criar usuários" },
        { name: "Listar usuário", slug: "r-user", description: "Permissão para listar usuários" },
        { name: "Editar usuário", slug: "u-user", description: "Permissão para editar usuários" },
        { name: "Excluir usuário", slug: "d-user", description: "Permissão para excluir usuários" },
        
        // Permissões de chat
        { name: "Criar chat", slug: "c-chat", description: "Permissão para criar chats" },
        { name: "Listar chat", slug: "r-chat", description: "Permissão para listar chats" },
        { name: "Editar chat", slug: "u-chat", description: "Permissão para editar chats" },
        { name: "Excluir chat", slug: "d-chat", description: "Permissão para excluir chats" },
        
        // Permissões de roles
        { name: "Criar role", slug: "c-role", description: "Permissão para criar roles" },
        { name: "Listar role", slug: "r-role", description: "Permissão para listar roles" },
        { name: "Editar role", slug: "u-role", description: "Permissão para editar roles" },
        { name: "Excluir role", slug: "d-role", description: "Permissão para excluir roles" },
        
        // Permissões de permissões
        { name: "Criar permissão", slug: "c-permission", description: "Permissão para criar permissões" },
        { name: "Listar permissão", slug: "r-permission", description: "Permissão para listar permissões" },
        { name: "Editar permissão", slug: "u-permission", description: "Permissão para editar permissões" },
        { name: "Excluir permissão", slug: "d-permission", description: "Permissão para excluir permissões" },
        
        // Permissões administrativas
        { name: "Acesso admin", slug: "admin", description: "Acesso total ao sistema" },
        { name: "Usuário padrão", slug: "user", description: "Acesso básico de usuário" },
        
        // Permissões de configurações
        { name: "Criar configuração", slug: "c-settings", description: "Permissão para criar configurações" },
        { name: "Listar configuração", slug: "r-settings", description: "Permissão para listar configurações" },
        { name: "Editar configuração", slug: "u-settings", description: "Permissão para editar configurações" },
        { name: "Excluir configuração", slug: "d-settings", description: "Permissão para excluir configurações" },
    ];

    // Criar todas as permissões
    const createdPermissions = await Promise.all(
        permissionsData.map((permission) =>
            prisma.permission.create({ data: permission })
        )
    );

    // Obter o ID do role Admin
    const adminRoleId = "aab56696-90a4-4b07-869d-7f051871c81c";

    // Associar todas as permissões ao role Admin
    await prisma.permissionRole.createMany({
        data: createdPermissions.map((permission) => ({
            permission_id: permission.id,
            role_id: adminRoleId,
        })),
    });

    console.log(`Criadas ${createdPermissions.length} permissões e associadas ao role Admin`);
}
