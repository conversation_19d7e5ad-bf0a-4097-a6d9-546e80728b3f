import { PrismaClient } from "@prisma/client";
import { permissions, roles, settings, users } from "./seeds";

export const prisma = new PrismaClient();

async function main() {
    console.log("seeding");

    await roles();
    await permissions();
    await users();
    await settings();
}

main()
    .catch((error) => {
        console.log(error);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });
